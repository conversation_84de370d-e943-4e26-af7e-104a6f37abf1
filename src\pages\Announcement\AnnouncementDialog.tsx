import React, { useEffect, useRef } from 'react';

// 扩展Window接口以支持Electron API
declare global {
  interface Window {
    electronAPI?: {
      closeApp: () => void;
    };
  }
}

interface AnnouncementDialogProps {
  isOpen: boolean;
  announcement: string;
  isLoading: boolean;
  error: string | null;
  onRetry: () => void;
  onClose?: () => void;
  // 激活相关props
  companyName: string;
  isActivationLoading: boolean;
  isQueryingCompany: boolean;
  isActivating: boolean;
  activationError: string | null;
  onCompanyNameChange: (name: string) => void;
  onActivate: () => void;
  onClearActivationError: () => void;
}

/**
 * 公告窗口组件
 * UI层：只负责渲染界面，不包含业务逻辑
 */
const AnnouncementDialog: React.FC<AnnouncementDialogProps> = ({
  isOpen,
  onClose,
  announcement,
  isLoading,
  error,
  onRetry,
  // 激活相关props
  companyName,
  isActivationLoading,
  isQueryingCompany,
  isActivating,
  activationError,
  onCompanyNameChange,
  onActivate,
  onClearActivationError
}) => {
  // 创建输入框的引用
  const companyNameInputRef = useRef<HTMLInputElement>(null);

  // 当对话框打开时，自动聚焦到公司名称输入框
  useEffect(() => {
    if (isOpen && companyNameInputRef.current) {
      // 使用 setTimeout 确保 DOM 已经渲染完成
      setTimeout(() => {
        companyNameInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onActivate();
  };

  const handleCompanyNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCompanyNameChange(e.target.value);
    if (activationError) {
      onClearActivationError();
    }
  };

  const isProcessing = isActivationLoading || isQueryingCompany || isActivating;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg w-[600px] h-[600px] relative overflow-hidden">

        <div className="relative flex flex-col h-full">

          {/* 内容区域 */}
          <div className="flex-1 flex flex-col px-4 pt-4 pb-3 space-y-3 overflow-hidden">

            {/* 系统公告部分 */}
            <div className="flex-1 bg-gray-50 border border-gray-200 rounded-lg shadow-sm relative overflow-hidden flex flex-col">
              {/* 公告标题 */}
              <div className="p-3 border-b border-gray-200 bg-blue-50 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h2 className="text-lg font-bold text-gray-800 translate-y-1">产品注册</h2>
                  </div>
                  {/* 关闭应用程序按钮 */}
                  <button
                    onClick={() => {
                      // 关闭整个应用程序
                      if (window.electronAPI?.closeApp) {
                        window.electronAPI.closeApp();
                      } else {
                        // 备用方案：直接关闭窗口
                        window.close();
                      }
                    }}
                    className="w-8 h-8 bg-transparent hover:bg-red-100 rounded-lg flex items-center justify-center transition-colors duration-200 group"
                    title="退出应用程序"
                  >
                    <svg className="w-5 h-5 text-red-600 group-hover:text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* 公告内容 */}
              <div className="p-6 flex-1 overflow-y-auto flex items-center justify-center min-h-[200px]">
                {isLoading && (
                  <div className="flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-3 border-blue-500 border-t-transparent mb-4"></div>
                    <span className="text-gray-600 font-medium text-base">正在获取公告...</span>
                  </div>
                )}

                {error && (
                  <div className="text-center">
                    <div className="mb-6">
                      <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-100 flex items-center justify-center">
                        <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <p className="text-red-600 font-semibold mb-2 text-base">获取公告失败</p>
                      <p className="text-sm text-gray-600 mb-6">{error}</p>
                    </div>
                    <button
                      onClick={onRetry}
                      className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-medium text-sm transition-colors duration-200 shadow-sm"
                    >
                      重新获取
                    </button>
                  </div>
                )}

                {!isLoading && !error && announcement && (
                  <div className="w-full max-w-lg mx-auto">
                    {/* 公告内容 */}
                    <div className="text-gray-800 text-lg leading-relaxed whitespace-pre-wrap text-center font-medium">
                      {announcement}
                    </div>
                  </div>
                )}

                {!isLoading && !error && !announcement && (
                  <div className="text-center text-gray-500">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p className="font-medium text-base text-gray-600">暂无公告信息</p>
                    <p className="text-sm text-gray-500 mt-2">系统将在有重要通知时显示公告</p>
                  </div>
                )}
              </div>
            </div>

            {/* 系统激活部分 */}
            <div className="flex-shrink-0 bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-lg shadow-sm relative overflow-hidden">
              {/* 装饰性背景 */}
              <div className="absolute top-0 right-0 w-24 h-24 opacity-10 pointer-events-none">
                <svg viewBox="0 0 100 100" className="w-full h-full text-green-500">
                  <circle cx="50" cy="50" r="40" fill="none" stroke="currentColor" strokeWidth="2" />
                  <circle cx="50" cy="50" r="25" fill="none" stroke="currentColor" strokeWidth="1" />
                  <circle cx="50" cy="50" r="10" fill="currentColor" />
                </svg>
              </div>



              {/* 激活表单内容 */}
              <div className="p-4 relative z-10">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="companyName" className="block text-base font-bold text-gray-700 mb-2 flex items-center">
                      <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                      </svg>
                      公司名称
                    </label>
                    <div className="relative">
                      <input
                        ref={companyNameInputRef}
                        type="text"
                        id="companyName"
                        value={companyName}
                        onChange={handleCompanyNameChange}
                        disabled={isProcessing}
                        placeholder="请输入您的公司全称"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white disabled:opacity-50 disabled:cursor-not-allowed text-gray-800 placeholder-gray-400 text-sm shadow-sm transition-all duration-200"
                        required
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {activationError && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-2">
                      <svg className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-xs text-red-700 font-medium flex-1">{activationError}</p>
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={isProcessing || !companyName.trim()}
                    className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 font-medium disabled:opacity-50 disabled:cursor-not-allowed text-sm shadow-sm transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100"
                  >
                    {isProcessing ? (
                      <span className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        <span>
                          {isQueryingCompany ? '正在验证公司信息...' :
                           isActivating ? '正在激活系统...' :
                           '处理中...'}
                        </span>
                      </span>
                    ) : (
                      <span className="flex items-center justify-center space-x-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        <span>激活系统</span>
                      </span>
                    )}
                  </button>

                  {/* 激活提示 */}
                  <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-green-600 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div className="text-xs text-green-700 leading-relaxed">
                        <p className="font-medium mb-1">激活说明：</p>
                        <p>• 请确保输入的公司名称准确无误</p>
                        <p>• 激活后系统将为您的公司进行个性化配置</p>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementDialog;
