import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { InventoryItem, DictionaryItem, ExtFieldDefinition, ExtFieldEditMode } from '../../../../types/inventory';
import { useInventory } from '../../../../hooks/Inventory/useInventory';
import InventoryService from '../../../../services/Inventory/inventoryService';
import DepartmentService, { DepartmentCategory } from '../../../../services/Inventory/departmentService';
import DatePicker from '../../../../components/Common/DatePicker';
import HierarchicalSelect from '../../../../components/HierarchicalSelect';
import DepartmentTreeSelect from '../../../../components/DepartmentTreeSelect';
import ResponsiblePersonSelect from '../../../../components/ResponsiblePersonSelect';
import ExtFieldDropdown from '../../../../components/ui/ExtFieldDropdown';
import HardDriveSerialField from '../../../../components/ui/HardDriveSerialField';
import IPAddressField from '../../../../components/ui/IPAddressField';
import MACAddressField from '../../../../components/ui/MACAddressField';
import { isTimeField } from '../../../../utils/fieldUtils';
import { ValidatedInput, ValidatedCombobox } from '../../../../components/ui/FormComponents';
import { getByteLength } from '../../../../utils/fieldValidation';

interface InventoryFormProps {
  initialData?: Partial<InventoryItem>;
  extFields?: ExtFieldDefinition[];  // 扩展字段定义
  extFieldValues?: Record<string, any>;  // 扩展字段值
  onExtFieldChange?: (key: string, value: any) => void;  // 扩展字段变更回调
  onSubmit: (data: Omit<InventoryItem, 'id'>) => void;
  onCancel: () => void;
  disabled?: boolean;
  disableTypeSelection?: boolean; // 是否禁用设备类型选择，用于从树状图联动添加时
  disableDepartmentSelection?: boolean; // 是否禁用部门选择，用于从树状图联动添加时
  disableResponsibleSelection?: boolean; // 是否禁用责任人选择，用于从树状图联动添加时
  hideButtons?: boolean; // 是否隐藏表单内的按钮，用于外部控制按钮显示

}

// 可编辑下拉框组件
interface ComboboxProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  options: DictionaryItem[];
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
}

const Combobox: React.FC<ComboboxProps> = ({
  name,
  value,
  onChange,
  options,
  placeholder,
  required = false,
  error,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(
    () => options.find(opt => opt.code === value)?.value || ''
  );
  const [filteredOptions, setFilteredOptions] = useState<DictionaryItem[]>(options);
  const [filteringMode, setFilteringMode] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  useEffect(() => {
    const selectedOption = options.find(opt => opt.code === value);
    const displayValue = selectedOption ? selectedOption.value : (value || '');
    if (inputValue !== displayValue) {
      setInputValue(displayValue);
      if (!isOpen) {
          setFilteredOptions(options);
      }
    }
  }, [value, options, inputValue, isOpen]);

  // 初始化过滤选项
  useEffect(() => {
    // 只在组件挂载时设置一次过滤选项，避免循环更新
    if (!filteringMode) {
      setFilteredOptions(options);
    }
  }, [options, filteringMode]);

  useEffect(() => {
    // 只在下拉框打开并处于过滤模式时过滤选项
    if (!isOpen || !filteringMode) {
      return;
    }

    // 使用防抖函数处理过滤操作
    const filterOptions = () => {
      if (inputValue.trim() === '') {
        setFilteredOptions(options);
      } else {
        const filtered = options.filter(option =>
          option.value.toLowerCase().includes(inputValue.toLowerCase())
        );
        setFilteredOptions(filtered);
      }
    };

    // 使用延时执行过滤，避免频繁更新
    const timeoutId = setTimeout(filterOptions, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [inputValue, isOpen, options, filteringMode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setFilteringMode(true);

    const matchingOption = options.find(opt => opt.value === newValue);
    const newCode = matchingOption ? matchingOption.code : newValue;

    const syntheticEvent = {
      target: { name, value: newCode },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);

    if (!isOpen) {
      setIsOpen(true);
    }
  };

  const handleFocus = () => {
    setFilteredOptions(options);
    setIsOpen(true);
    setFilteringMode(false);
  };

  const selectOption = (option: DictionaryItem) => {
    setInputValue(option.value);
    setIsOpen(false);
    setFilteringMode(false);

    const syntheticEvent = {
      target: { name, value: option.code },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);

    setFilteredOptions(options);
  };

  // 计算下拉菜单位置
  useEffect(() => {
    if (isOpen && inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  }, [isOpen]);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    // 定义处理点击外部的函数
    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideInput = inputRef.current && !inputRef.current.contains(event.target as Node);
      const isOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(event.target as Node);

      if (isOutsideInput && (isOutsideDropdown || !isOpen)) {
        if (isOpen) {
            setIsOpen(false);
            setFilteringMode(false);

            // 恢复为正确的显示值
            const currentSelectedOption = options.find(opt => opt.code === value);
            const correctDisplayValue = currentSelectedOption ? currentSelectedOption.value : (value || '');

            // 使用函数形式的setState避免依赖inputValue
            setInputValue(prevValue => {
              if (prevValue !== correctDisplayValue) {
                return correctDisplayValue;
              }
              return prevValue;
            });

            // 重置过滤选项
            setFilteredOptions(options);
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [value, options, isOpen]); // 移除inputValue依赖

  return (
    <div className="relative">
      <div className="flex">
        <input
          ref={inputRef}
          type="text"
          name={name}
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          placeholder={placeholder}
          className={`w-full px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500
            ${error ? 'border-red-300' : 'border-gray-300'}`}
          autoComplete="off"
          disabled={disabled}
        />
        <button
          type="button"
          onClick={() => {
            setFilteredOptions(options);
            setIsOpen(!isOpen);
            setFilteringMode(false);
          }}
          className="absolute right-1.5 top-1/2 transform -translate-y-1/2 text-gray-400"
          disabled={disabled}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-4 w-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {isOpen && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9000] max-h-60 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
        >
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option) => (
              <div
                key={option.code}
                className="px-3 py-1.5 cursor-pointer hover:bg-blue-50"
                onClick={() => selectOption(option)}
              >
                {option.value}
              </div>
            ))
          ) : (
            <div className="px-3 py-1.5 text-gray-500">无匹配选项</div>
          )}
        </div>,
        document.body
      )}

      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

// 纯下拉选择框组件
interface SelectProps {
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  options: DictionaryItem[];
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
}

const Select: React.FC<SelectProps> = ({
  name,
  value,
  onChange,
  options,
  placeholder,
  required = false,
  error,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [displayValue, setDisplayValue] = useState(
    () => options.find(opt => opt.code === value)?.value || placeholder || ''
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });

  useEffect(() => {
    const selectedOption = options.find(opt => opt.code === value);
    const newDisplayValue = selectedOption ? selectedOption.value : (placeholder || '');
    setDisplayValue(newDisplayValue);
  }, [value, options, placeholder]);

  const selectOption = (option: DictionaryItem) => {
    setIsOpen(false);

    const syntheticEvent = {
      target: { name, value: option.code },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);
  };

  // 计算下拉菜单位置
  useEffect(() => {
    if (isOpen && selectRef.current) {
      const rect = selectRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      });
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node) &&
          dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative">
      <div
        ref={selectRef}
        className={`w-full px-3 py-1.5 border rounded-md focus:outline-none cursor-pointer flex justify-between items-center
          ${error ? 'border-red-300' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <span className={`${!value ? 'text-gray-400' : 'text-gray-700'}`}>
          {displayValue}
        </span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </div>

      {isOpen && !disabled && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9000] max-h-60 overflow-auto bg-white border border-gray-300 rounded-md shadow-lg"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }}
        >
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={option.code}
                className="px-3 py-1.5 cursor-pointer hover:bg-blue-50"
                onClick={() => selectOption(option)}
              >
                {option.value}
              </div>
            ))
          ) : (
            <div className="px-3 py-1.5 text-gray-500">无选项</div>
          )}
        </div>,
        document.body
      )}

      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
    </div>
  );
};

// 动态扩展字段组件
interface ExtFieldProps {
  field: ExtFieldDefinition;
  value: any;
  onChange: (key: string, value: any) => void;
  disabled?: boolean;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
}

const ExtField: React.FC<ExtFieldProps> = ({ field, value, onChange, disabled = false, errors, setErrors }) => {
  // 统一的错误处理
  const errorKey = `ext_${field.key}`;
  const hasError = errors[errorKey];

  // 统一的变更处理函数
  const handleChange = (newValue: string) => {
    onChange(field.key, newValue);
    // 统一的错误清除逻辑
    if (hasError) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    }
  };

  // 首先根据编辑模式决定渲染方式
  if (field.editMode === ExtFieldEditMode.SelectOnly) {
    // 不可编辑模式 - 只能从下拉选项中选择
    return (
      <>
        <label className="block text-sm font-bold text-gray-700 mb-2">
          {field.title} {field.required && <span className="text-red-500">*</span>}
        </label>
        <ExtFieldDropdown
          value={value || ''}
          onChange={(selectedValue) => handleChange(selectedValue)}
          options={field.options || []}
          placeholder="请选择"
          disabled={disabled}
        />
        {hasError && (
          <p className="mt-1 text-sm text-red-600">{hasError}</p>
        )}
      </>
    );
  }

  // 可编辑模式或必选模式 - 根据字段类型和字段名称渲染不同的输入组件
  // 优先检查字段名称是否包含时间关键词，如果包含则使用时间选择器
  if (isTimeField(field.title) || isTimeField(field.key) || field.type === 'date') {
    // 处理日期值，确保格式正确
    let dateValue = '';
    if (value) {
      // 如果是数字或数字字符串（时间戳），转换为日期格式
      if (typeof value === 'number' || (typeof value === 'string' && /^\d+$/.test(value))) {
        const timestamp = typeof value === 'string' ? parseInt(value) : value;
        // 判断是秒级还是毫秒级时间戳
        const milliseconds = timestamp.toString().length <= 10 ? timestamp * 1000 : timestamp;
        const date = new Date(milliseconds);
        if (!isNaN(date.getTime())) {
          // 转换为 YYYY-MM-DDTHH:mm:ss 格式
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          dateValue = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
        }
      } else {
        // 如果已经是字符串格式，直接使用
        dateValue = String(value);
      }
    }

    return (
      <>
        <label className="block text-sm font-bold text-gray-700 mb-2">
          {field.title} {field.required && <span className="text-red-500">*</span>}
        </label>
        <DatePicker
          value={dateValue}
          onChange={(date) => handleChange(date)}
          placeholder={`请选择${field.title}`}
          disabled={disabled}
          error={!!hasError}
          showTime={true}
          showSeconds={true}
          className="text-sm"
        />
        {hasError && (
          <p className="mt-1 text-sm text-red-600">{hasError}</p>
        )}
      </>
    );
  }

  // 特殊字段处理 - 硬盘序列号（该组件有内置标签）
  if (field.title === '硬盘序列号' || field.key === 'hard_drive_serial' || field.title.includes('硬盘序列号')) {
    return (
      <>
        <HardDriveSerialField
          value={value || ''}
          onChange={(newValue) => handleChange(newValue)}
          disabled={disabled}
          required={field.required}
          error={hasError}
        />
        {hasError && (
          <p className="mt-1 text-sm text-red-600">{hasError}</p>
        )}
      </>
    );
  }

  // 特殊字段处理 - IP地址（该组件有内置标签）
  if (field.title.toLowerCase().includes('ip') || field.key.toLowerCase().includes('ip')) {
    return (
      <>
        <IPAddressField
          label={field.title}
          value={value || ''}
          onChange={(newValue) => handleChange(newValue)}
          disabled={disabled}
          error={hasError}
          required={field.required}
        />
        {hasError && (
          <p className="mt-1 text-sm text-red-600">{hasError}</p>
        )}
      </>
    );
  }

  // 特殊字段处理 - MAC地址（该组件有内置标签）
  if (field.title.toLowerCase().includes('mac') || field.key.toLowerCase().includes('mac')) {
    return (
      <>
        <MACAddressField
          label={field.title}
          value={value || ''}
          onChange={(newValue) => handleChange(newValue)}
          disabled={disabled}
          error={hasError}
          required={field.required}
        />
        {hasError && (
          <p className="mt-1 text-sm text-red-600">{hasError}</p>
        )}
      </>
    );
  }

  // 根据字段类型渲染其他类型的输入组件
  switch (field.type) {
    case 'text':
      return (
        <>
          <label className="block text-sm font-bold text-gray-700 mb-2">
            {field.title} {field.required && <span className="text-red-500">*</span>}
          </label>
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              hasError
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            disabled={disabled}
          />
          {hasError && (
            <p className="mt-1 text-sm text-red-600">{hasError}</p>
          )}
        </>
      );

    case 'number':
      return (
        <>
          <label className="block text-sm font-bold text-gray-700 mb-2">
            {field.title} {field.required && <span className="text-red-500">*</span>}
          </label>
          <input
            type="number"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              hasError
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            disabled={disabled}
          />
          {hasError && (
            <p className="mt-1 text-sm text-red-600">{hasError}</p>
          )}
        </>
      );

    case 'select':
      return (
        <>
          <label className="block text-sm font-bold text-gray-700 mb-2">
            {field.title} {field.required && <span className="text-red-500">*</span>}
          </label>
          <ExtFieldDropdown
            value={value || ''}
            onChange={(selectedValue) => handleChange(selectedValue)}
            options={field.options || []}
            placeholder="请选择"
            disabled={disabled}
          />
          {hasError && (
            <p className="mt-1 text-sm text-red-600">{hasError}</p>
          )}
        </>
      );

    default:
      return (
        <>
          <label className="block text-sm font-bold text-gray-700 mb-2">
            {field.title} {field.required && <span className="text-red-500">*</span>}
          </label>
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              hasError
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            disabled={disabled}
          />
          {hasError && (
            <p className="mt-1 text-sm text-red-600">{hasError}</p>
          )}
        </>
      );
  }
};

const InventoryForm: React.FC<InventoryFormProps> = ({
  initialData = {},
  extFields = [],
  extFieldValues = {},
  onExtFieldChange = () => {},
  onSubmit,
  onCancel,
  disabled = false,
  disableTypeSelection = false,
  disableDepartmentSelection = false,
  disableResponsibleSelection = false,
  hideButtons = false
}) => {
  const {
    fieldOptions,
    getDictionaryItems,
    getCategoryExtFields,
    setFormExtFields, // 这里实际上是 updateFormExtFields 的备注
    clearFormExtFields,
    formExtFields,
  } = useInventory();

  // 获取部门服务 - 使用服务实例直接获取部门树，避免使用 useDepartment 导致的循环依赖
  const departmentService = useMemo(() => DepartmentService.getInstance(), []);
  const [departmentCategories, setDepartmentCategories] = useState<DepartmentCategory[]>([]);

  // 在组件挂载时加载部门树
  useEffect(() => {
    let isMounted = true;

    const loadDepartmentTree = async () => {
      try {
        console.log('开始加载部门树...');
        // 直接从服务中获取当前部门树状态
        const currentState = departmentService.getState();
        console.log('当前部门树状态:', currentState.departmentCategories.length > 0 ? '有数据' : '无数据');

        // 如果已经有数据，直接使用
        if (currentState.departmentCategories.length > 0) {
          if (isMounted) {
            console.log('使用现有部门树数据');
            setDepartmentCategories(currentState.departmentCategories);
          }
        } else {
          // 如果没有数据，等待 InventoryService 的延迟加载完成
          console.log('部门树数据为空，等待 InventoryService 延迟加载完成...');

          // 设置一个监听器，等待部门树加载完成
          let retryCount = 0;
          const maxRetries = 15; // 最多等待15秒
          const checkInterval = 1000; // 每秒检查一次

          const checkDepartmentTree = () => {
            if (!isMounted) return;

            const currentState = departmentService.getState();
            if (currentState.departmentCategories.length > 0) {
              console.log('等待完成，部门树已加载，使用数据');
              setDepartmentCategories(currentState.departmentCategories);
              return;
            }

            retryCount++;
            if (retryCount < maxRetries) {
              console.log(`等待部门树加载中... (${retryCount}/${maxRetries})`);
              setTimeout(checkDepartmentTree, checkInterval);
            } else {
              console.warn('等待部门树加载超时，使用空的部门树');
              // 超时后使用空的部门树
              if (isMounted) {
                const emptyTree: DepartmentCategory[] = [{
                  id: 'all-dept',
                  name: '全部部门',
                  count: 0,
                  children: []
                }];
                setDepartmentCategories(emptyTree);
              }
            }
          };

          // 开始检查
          setTimeout(checkDepartmentTree, 1500); // 延迟1.5秒开始检查，给 InventoryService 足够时间
        }
      } catch (error) {
        console.error('加载部门树失败:', error);
        // 创建一个空的部门树作为备用
        if (isMounted) {
          const emptyTree: DepartmentCategory[] = [{
            id: 'all-dept',
            name: '全部部门',
            count: 0,
            children: []
          }];
          setDepartmentCategories(emptyTree);
        }
      }
    };

    // 使用setTimeout延迟加载，确保在渲染周期外调用
    const timer = setTimeout(() => {
      loadDepartmentTree();
    }, 0);

    // 添加事件监听，当部门树更新时更新状态
    const handleTreeUpdated = (categories: DepartmentCategory[]) => {
      if (isMounted) {
        console.log('部门树数据已更新');
        setDepartmentCategories(categories);
      }
    };

    departmentService.on('tree-updated', handleTreeUpdated);
    departmentService.on('tree-loaded', handleTreeUpdated);

    return () => {
      isMounted = false;
      clearTimeout(timer);
      departmentService.off('tree-updated', handleTreeUpdated);
      departmentService.off('tree-loaded', handleTreeUpdated);
    };
  }, [departmentService]);

  // 从部门树中获取部门和人员选项 - 使用缓存优化性能
  const [departmentOptions, setDepartmentOptions] = useState<DictionaryItem[]>([]);
  const [personOptions, setPersonOptions] = useState<DictionaryItem[]>([]);

  // 存储所有人员选项，用于根据部门筛选
  const [allPersonOptions, setAllPersonOptions] = useState<{
    personName: string;
    departmentId: string;
    departmentName: string;
    personId: string;
    fullName: string;
  }[]>([]);

  // 部门树选项 - 用于DepartmentTreeSelect组件
  interface DepartmentOption {
    id: string;
    value: string;
    label: string;
    children?: DepartmentOption[];
  }
  const [departmentTreeOptions, setDepartmentTreeOptions] = useState<DepartmentOption[]>([]);

  // 当部门树变化时更新选项
  useEffect(() => {
    // 初始化部门和人员选项数组
    const deptOptions: DictionaryItem[] = [];
    const persOptions: DictionaryItem[] = [];
    const allPersons: {
      personName: string;
      departmentId: string;
      departmentName: string;
      personId: string;
      fullName: string;
    }[] = [];

    // 如果部门树不为空，则提取部门和人员
    if (departmentCategories.length > 0) {
      // 递归遍历部门树，一次性提取所有部门和人员
      const extractAll = (categories: DepartmentCategory[], parentDept?: { id: string, name: string }) => {
        for (const category of categories) {
          try {
            // 排除根节点
            if (category.id !== 'all-dept') {
              // 如果是部门节点
              if (category.id.startsWith('dept-')) {
                // 确保部门名称不重复
                if (!deptOptions.some(opt => opt.code === category.name)) {
                  deptOptions.push({
                    code: category.name,
                    value: category.name
                  });

                }

                // 如果有子节点，递归处理，并传递当前部门作为父部门
                if (category.children && category.children.length > 0) {
                  // 处理直属人员节点
                  const personNodes = category.children.filter(child => child.id.startsWith('person-'));
                  for (const personNode of personNodes) {
                    // 处理人员名称和备注
                    let personName = personNode.name;

                    // 添加到所有人员列表，包含部门信息
                    allPersons.push({
                      personName: personName,
                      departmentId: category.id,
                      departmentName: category.name,
                      personId: personNode.id,
                      fullName: personName
                    });

                    // 添加到人员选项列表（所有人员）
                    if (!persOptions.some(opt => opt.code === personName)) {
                      persOptions.push({
                        code: personName,
                        value: personName
                      });

                    }
                  }

                  // 递归处理子部门
                  const deptNodes = category.children.filter(child => child.id.startsWith('dept-'));
                  extractAll(deptNodes, { id: category.id, name: category.name });
                }
              }
            } else if (category.children && category.children.length > 0) {
              // 如果是根节点，只处理其子节点
              extractAll(category.children);
            }
          } catch (error) {
            console.error('处理部门树节点时出错:', error, category);
          }
        }
      };



      // 构建部门树选项 - 专用于DepartmentTreeSelect组件
      const buildDepartmentTreeOptions = (categories: DepartmentCategory[]): DepartmentOption[] => {
        const result: DepartmentOption[] = [];

        for (const category of categories) {
          // 排除根节点
          if (category.id === 'all-dept') {
            // 如果是根节点，只处理其子节点
            if (category.children && category.children.length > 0) {
              const childOptions = buildDepartmentTreeOptions(category.children);
              result.push(...childOptions);
            }
          } else if (category.id.startsWith('dept-')) {
            // 如果是部门节点
            const hasChildren = category.children && category.children.length > 0;

            // 过滤子节点，只保留部门节点（排除人员节点）
            const deptChildren = hasChildren
              ? category.children.filter(child => child.id.startsWith('dept-'))
              : [];

            const deptOption: DepartmentOption = {
              id: category.id,
              value: category.name,
              label: category.name,
              children: deptChildren.length > 0 ? buildDepartmentTreeOptions(deptChildren) : undefined
            };

            result.push(deptOption);
          }
          // 忽略人员节点，因为部门选择不需要显示人员
        }

        return result;
      };

      // 遍历部门树，提取部门和人员
      try {
        extractAll(departmentCategories);

        // 构建部门树选项
        const treeOpts = buildDepartmentTreeOptions(departmentCategories);
        setDepartmentTreeOptions(treeOpts);
      } catch (error) {
        console.error('遍历部门树时出错:', error);
      }
    }

    // 按名称排序
    deptOptions.sort((a, b) => a.value.localeCompare(b.value, 'zh-CN'));
    persOptions.sort((a, b) => a.value.localeCompare(b.value, 'zh-CN'));

    // 更新状态
    setDepartmentOptions(deptOptions);
    setPersonOptions([]); // 初始化为空，等待选择部门后再填充
    setAllPersonOptions(allPersons); // 保存所有人员信息，包含部门关系
  }, [departmentCategories]);

  // 从设备分类树中获取设备类型选项 - 使用状态缓存
  // 注意：这里使用了useState但主要通过hierarchicalDeviceTypeOptions来使用
  const [, setDeviceTypeOptions] = useState<DictionaryItem[]>([]);

  // 分层选项接口
  interface HierarchicalOption {
    id: string | number;
    value: string;
    label: string;
    isParent?: boolean;
    parentId?: string | number;
    children?: HierarchicalOption[];
  }

  // 层级结构的设备类型选项 - 用于HierarchicalSelect组件
  const [hierarchicalDeviceTypeOptions, setHierarchicalDeviceTypeOptions] = useState<HierarchicalOption[]>([]);

  // 使用useRef跟踪是否已经加载过设备类型选项
  const deviceTypeOptionsLoadedRef = useRef<boolean>(false);

  // 存储设备类型到一级分类的映射关系，避免重复查找
  const deviceTypeToParentRef = useRef<Map<string, string>>(new Map());

  // 防抖函数
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // 使用useCallback包装loadDeviceTypeOptions函数，避免重复创建
  const loadDeviceTypeOptions = useCallback(() => {
    try {
      const inventoryService = InventoryService.getInstance();
      // 使用安全的方式获取deviceCategories
      const deviceCategories = inventoryService?.getState?.()?.deviceCategories || [];

      const options: DictionaryItem[] = [];
      const hierarchicalOptions: HierarchicalOption[] = [];
      const deviceTypeToParentMap = new Map<string, string>();

      // 遍历设备分类树，提取二级分类
      if (deviceCategories.length > 0 && deviceCategories[0]?.children) {
        // 创建一级分类映射
        const parentCategoryMap = new Map<string, HierarchicalOption>();

        // 用于检查重复的设备类型
        const existingDeviceTypes = new Set<string>();

        for (const parent of deviceCategories[0].children) {
          if (parent?.children) {
            // 创建一级分类选项 - 作为父节点
            const parentOption: HierarchicalOption = {
              id: `parent-${parent.id}`,
              value: parent.name, // 值不会被选中，因为不会显示在选项中
              label: parent.name,
              isParent: true,
              children: []
            };

            // 将一级分类添加到顶层选项中
            hierarchicalOptions.push(parentOption);

            // 添加到映射中，方便后续添加子选项
            parentCategoryMap.set(parent.name, parentOption);

            // 处理二级分类
            for (const child of parent.children) {
              // 检查是否已存在相同的设备类型
              if (!existingDeviceTypes.has(child.name)) {
                existingDeviceTypes.add(child.name);

                // 添加到普通选项中
                options.push({
                  code: child.name,
                  value: child.name
                });

                // 添加到层级选项中
                const childOption: HierarchicalOption = {
                  id: child.id,
                  value: child.name,
                  label: child.name,
                  parentId: parent.id
                };

                // 不再将二级分类直接添加到顶层选项中，而是作为一级分类的子选项
                // hierarchicalOptions.push(childOption);

                // 添加到父选项的children中
                const parentOpt = parentCategoryMap.get(parent.name);
                if (parentOpt && parentOpt.children) {
                  parentOpt.children.push(childOption);
                }

                // 存储设备类型到一级分类的映射关系
                deviceTypeToParentMap.set(child.name, parent.name);
              }
            }
          }
        }
      }

      // 更新设备类型到一级分类的映射关系
      deviceTypeToParentRef.current = deviceTypeToParentMap;

      // 使用函数形式的setState，避免依赖于deviceTypeOptions
      setDeviceTypeOptions(prevOptions => {
        // 只有当选项发生变化时才更新
        if (JSON.stringify(prevOptions) !== JSON.stringify(options)) {
          return options;
        }
        return prevOptions;
      });

      // 更新层级结构的选项
      setHierarchicalDeviceTypeOptions(prevOptions => {
        // 只有当选项发生变化时才更新
        if (JSON.stringify(prevOptions) !== JSON.stringify(hierarchicalOptions)) {
          return hierarchicalOptions;
        }
        return prevOptions;
      });

      // 标记为已加载
      deviceTypeOptionsLoadedRef.current = true;

      console.log('设备类型选项加载完成，共', options.length, '个选项');
    } catch (error) {
      console.error('获取设备类型选项失败:', error);
    }
  }, []);

  // 在组件挂载时加载设备类型选项
  useEffect(() => {
    // 使用setTimeout延迟加载，确保在渲染周期外调用
    const timer = setTimeout(() => {
      loadDeviceTypeOptions();
    }, 0);

    return () => clearTimeout(timer);
  }, [loadDeviceTypeOptions]);

  // 添加事件监听，当设备分类树更新时更新选项
  useEffect(() => {
    const inventoryService = InventoryService.getInstance();

    // 使用防抖处理，避免短时间内多次触发
    const handleStateChange = () => {
      // 清除之前的定时器
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      // 设置新的定时器，300ms后执行
      debounceRef.current = setTimeout(() => {
        console.log('设备分类树状态变化，重新加载设备类型选项');
        // 重置加载标志，强制重新加载
        deviceTypeOptionsLoadedRef.current = false;
        loadDeviceTypeOptions();
      }, 300);
    };

    inventoryService.on('state-change', handleStateChange);

    return () => {
      inventoryService.off('state-change', handleStateChange);
      // 清除定时器
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [loadDeviceTypeOptions]);

  const statusDictionaryItems = getDictionaryItems('STATUS');
  const securityLevelDictionaryItems = getDictionaryItems('SECURITY_LEVEL');

  const [formData, setFormData] = useState<Omit<InventoryItem, 'id'>>({
    name: '',
    type: '',
    status: '',

    model: '',
    department: '',
    responsible: '',
    location: '',
    startTime: '',
    securityCode: '',
    securityLevel: '',
    purpose: '',
    securityRfid: '',
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 通用的onChange包装器，避免重复代码
  const createOnChangeHandler = (fieldName: string) => (value: string) => {
    const syntheticEvent = {
      target: { name: fieldName, value }
    } as React.ChangeEvent<HTMLInputElement>;
    handleInputChange(syntheticEvent);
  };

  // 更新表单数据的函数
  const updateFormData = useCallback((updates: Partial<InventoryItem>) => {
    // 检查updates是否有效
    if (!updates || typeof updates !== 'object') {
      console.warn('updateFormData: 无效的更新数据', updates);
      return;
    }

    setFormData(prev => ({
      ...prev,
      ...updates
    }));

    // 清除相关字段的错误
    const updatedFields = Object.keys(updates);
    if (updatedFields.length > 0) {
      setErrors(prev => {
        const newErrors = { ...prev };
        updatedFields.forEach(field => {
          delete newErrors[field];
        });
        return newErrors;
      });
    }
  }, []);

  // 根据部门名称获取该部门的直属责任人
  const getPersonsByDepartment = (departmentName: string): DictionaryItem[] => {
    if (!departmentName) return [];

    // 查找部门ID - 考虑可能存在多个同名部门的情况
    let departmentIds: string[] = [];

    // 递归查找所有匹配名称的部门ID
    const findAllDeptIds = (cats: DepartmentCategory[]): void => {
      for (const cat of cats) {
        if (cat.id.startsWith('dept-') && cat.name === departmentName) {
          departmentIds.push(cat.id);
        }
        if (cat.children?.length) {
          findAllDeptIds(cat.children);
        }
      }
    };

    // 在整个部门树中查找
    for (const category of departmentCategories) {
      findAllDeptIds([category]);
    }

    if (departmentIds.length === 0) {
      console.warn(`未找到部门: ${departmentName}`);
      return [];
    }

    // 过滤出这些部门的直属责任人
    const directPersons = allPersonOptions
      .filter(person => departmentIds.includes(person.departmentId))
      .map(person => ({
        code: person.fullName,
        value: person.fullName
      }));

    return directPersons;
  };

  // 当部门变化时更新责任人选项
  useEffect(() => {
    // 确保部门树已加载
    if (departmentCategories.length === 0) {
      return;
    }

    if (formData.department) {
      // 获取部门下的人员
      const departmentPersons = getPersonsByDepartment(formData.department);
      setPersonOptions(departmentPersons);

      // 如果有预设的责任人，检查是否在选项列表中
      if (formData.responsible) {
        const responsibleExists = departmentPersons.some(p => p.code === formData.responsible);

        // 如果当前选中的责任人不在新的选项列表中，清空责任人选择
        if (!responsibleExists) {
          setFormData(prev => ({
            ...prev,
            responsible: ''
          }));
        }
      }
    } else {
      // 如果没有选择部门，清空责任人选项
      setPersonOptions([]);

      // 同时清空责任人选择
      if (formData.responsible) {
        setFormData(prev => ({
          ...prev,
          responsible: ''
        }));
      }
    }
  }, [formData.department, formData.responsible, allPersonOptions, departmentCategories]);

  // 使用ref记录上一次的数据
  const prevDataRef = useRef<string>(JSON.stringify(initialData));

  // 跟踪扩展字段的变化
  useEffect(() => {
    console.log('表单扩展字段变化:', formExtFields);
  }, [formExtFields]);

  // 加载设备类型的扩展字段
  const loadExtFieldsForDeviceType = useCallback(async (deviceType: string) => {
    console.log(`开始加载设备类型 [${deviceType}] 的扩展字段`);
    if (!deviceType) {
      console.log('设备类型为空，不加载扩展字段');
      return;
    }

    try {
      // 检查设备类型是否是一级分类
      const inventoryService = InventoryService.getInstance();
      const deviceCategories = inventoryService?.getState?.()?.deviceCategories || [];

      // 如果设备分类树为空，直接返回
      if (!deviceCategories.length || !deviceCategories[0]?.children) {
        console.log('设备分类树为空，不加载扩展字段');
        return;
      }

      // 检查是否是一级分类
      let isParentCategory = false;
      let parentCategory = '';
      let subCategory = deviceType;

      // 首先检查是否是一级分类
      for (const parent of deviceCategories[0].children) {
        if (parent.name === deviceType) {
          isParentCategory = true;
          parentCategory = deviceType;
          subCategory = ''; // 一级分类没有子分类
          break;
        }
      }

      // 如果不是一级分类，则尝试查找其所属的一级分类
      if (!isParentCategory) {
        // 使用映射关系直接获取一级分类，避免遍历整个设备分类树
        parentCategory = deviceTypeToParentRef.current.get(deviceType) || '';

        // 如果映射中没有找到，尝试从设备分类树中查找（作为备用方案）
        if (!parentCategory) {
          console.log('在映射中未找到设备类型的一级分类，尝试从设备分类树中查找');

          // 遍历设备分类树，查找对应的一级分类
          for (const parent of deviceCategories[0].children) {
            if (parent?.children) {
              for (const child of parent.children) {
                if (child.name === deviceType) {
                  parentCategory = parent.name;
                  // 更新映射关系，避免下次再查找
                  deviceTypeToParentRef.current.set(deviceType, parentCategory);
                  break;
                }
              }
              if (parentCategory) break;
            }
          }
        }
      }

      if (!parentCategory) {
        console.log(`未找到设备类型 [${deviceType}] 的一级分类`);
        return;
      }

      console.log(`开始加载设备类型 [${deviceType}] 的扩展字段，一级分类：[${parentCategory}]`);

      // 获取扩展字段定义
      const extFields = await getCategoryExtFields(parentCategory, subCategory);
      console.log(`获取到 ${extFields.length} 个扩展字段定义:`, extFields);

      // 设置表单扩展字段
      setFormExtFields(extFields);
      console.log('已设置表单扩展字段');
    } catch (error) {
      console.error('加载扩展字段失败:', error);
    }
  }, [getCategoryExtFields, setFormExtFields]);

  // 在表单打开时，根据当前选择的设备类型加载扩展字段
  useEffect(() => {
    // 如果有设备类型，则加载对应的扩展字段
    if (formData.type) {
      console.log(`表单打开时加载设备类型 [${formData.type}] 的扩展字段`);
      loadExtFieldsForDeviceType(formData.type);
    } else {
      // 如果没有设备类型，清除表单扩展字段
      console.log('表单打开时没有设备类型，清除表单扩展字段');
      clearFormExtFields();
    }
  }, [formData.type, loadExtFieldsForDeviceType, clearFormExtFields]); // 当设备类型变化时重新加载

  // 使用ref记录是否已经初始化
  const initializedRef = useRef<boolean>(false);

  // 只在初始数据变化时更新表单数据
  useEffect(() => {
    // 确保部门树已加载
    if (departmentCategories.length === 0) {
      return;
    }

    // 使用JSON.stringify比较对象是否发生变化
    const currentDataStr = JSON.stringify(initialData);

    // 如果数据没有变化且已经初始化过，则跳过
    if (currentDataStr === prevDataRef.current && initializedRef.current) {
      return;
    }

    // 更新表单数据
    // 如果initialData为空对象，则重置表单数据；否则合并数据
    if (Object.keys(initialData).length === 0) {
      // 重置为默认值
      setFormData({
        name: '',
        type: '',
        status: '',
        model: '',
        department: '',
        responsible: '',
        location: '',
        startTime: '',
        securityCode: '',
        securityLevel: '',
        purpose: '',
        securityRfid: ''
      });
    } else {
      // 合并初始数据
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }

    // 记录当前数据和初始化状态
    prevDataRef.current = currentDataStr;
    initializedRef.current = true;

    // 如果有部门信息，预加载该部门下的人员选项
    if (initialData.department) {
      const departmentPersons = getPersonsByDepartment(initialData.department);
      setPersonOptions(departmentPersons);
    }

    // 如果初始数据中有设备类型，加载对应的扩展字段
    if (initialData.type) {
      loadExtFieldsForDeviceType(initialData.type);
    }
  }, [initialData, departmentCategories, getPersonsByDepartment, loadExtFieldsForDeviceType]);



  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // 更新表单数据
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 清除错误
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // 如果是设备类型字段变更，加载对应的扩展字段
    if (name === 'type' && value) {
      console.log(`设备类型变更为 [${value}]，加载对应的扩展字段`);
      loadExtFieldsForDeviceType(value);
    }

    // 责任人自动匹配部门的功能已移除
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) { newErrors.name = '名称不能为空'; }
    if (!formData.type.trim()) { newErrors.type = '设备类型不能为空'; }
    if (!formData.status) { newErrors.status = '使用情况不能为空'; }
    if (!formData.model.trim()) { newErrors.model = '型号不能为空'; }

    // 保密编号、所属部门、责任人设置为必填项
    if (!formData.securityCode.trim()) { newErrors.securityCode = '保密编号不能为空'; }
    if (!formData.department.trim()) { newErrors.department = '所属部门不能为空'; }
    if (!formData.responsible.trim()) { newErrors.responsible = '责任人不能为空'; }

    if (!formData.location.trim()) { newErrors.location = '放置地点不能为空'; }
    if (formData.securityLevel === '' || formData.securityLevel === null || formData.securityLevel === undefined) {
      newErrors.securityLevel = '密级不能为空';
    }
    if (!formData.purpose.trim()) {
      newErrors.purpose = '用途不能为空';
    }

    // 启用时间验证
    if (!formData.startTime || formData.startTime.trim() === '') {
      newErrors.startTime = '启用时间不能为空';
    } else if (!/^\d{4}-\d{2}-\d{2}$/.test(formData.startTime)) {
      newErrors.startTime = '日期格式无效，请使用YYYY-MM-DD格式';
    }

    // 验证必填的扩展字段
    extFields.forEach(field => {
      if (field.required) {
        const value = extFieldValues[field.key];
        // 检查值是否为空（支持字符串、数字、日期等类型）
        const isEmpty = !value ||
          (typeof value === 'string' && value.trim() === '') ||
          (typeof value === 'number' && isNaN(value)) ||
          (value instanceof Date && isNaN(value.getTime()));

        if (isEmpty) {
          newErrors[`ext_${field.key}`] = `${field.title}不能为空`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (disabled) {
      return; // 如果禁用，不提交表单
    }

    if (validateForm()) {
      // 过滤掉空值字段，只保留有值的字段
      const filteredData: Record<string, any> = {};

      // 遍历表单数据，只保留非空字段
      Object.entries(formData).forEach(([key, value]) => {
        // 如果值不为空，或者是必填字段，则保留
        const isRequired = [
          'name', 'type', 'status', 'model', 'department',
          'responsible', 'location', 'securityLevel', 'purpose'
        ].includes(key);

        // 判断值是否为空
        const isEmpty =
          value === undefined ||
          value === null ||
          (typeof value === 'string' && value.trim() === '');

        // 如果不为空或者是必填字段，则保留
        if (!isEmpty || isRequired) {
          filteredData[key] = value;
        }
      });

      // 提取责任人备注
      if (filteredData.responsible && typeof filteredData.responsible === 'string') {
        const responsibleValue = filteredData.responsible;

        // 检查是否包含括号，提取备注
        if (responsibleValue.includes(' (')) {
          const aliasMatch = responsibleValue.match(/\(([^)]+)\)/);
          if (aliasMatch && aliasMatch[1]) {
            // 添加责任人备注字段
            filteredData.responsible_person_alias = aliasMatch[1];

            // 从责任人字段中提取姓名部分（不包含括号和备注）
            const nameMatch = responsibleValue.match(/^(.*?)\s*\(/);
            if (nameMatch && nameMatch[1]) {
              // 更新责任人字段为仅包含姓名的部分
              filteredData.responsible_person_name = nameMatch[1].trim();
            }
          }
        }
      }

      console.log('过滤后的表单数据:', filteredData);
      onSubmit(filteredData as Omit<InventoryItem, 'id'>);
    }
  };

  return (
    <div className={hideButtons ? "" : "flex flex-col h-full"}>
      <form onSubmit={handleSubmit} className={hideButtons ? "space-y-4" : "flex flex-col h-full"}>
        {/* 表单字段区域 - 根据是否隐藏按钮决定布局 */}
        <div className={hideButtons ? "" : "flex-1 overflow-y-auto min-h-0 pb-4"}>
          {/* 统一的网格布局容器 - 包含所有字段 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5">
            {/* 设备类型 */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-gray-700 mb-2">
                设备类型 <span className="text-red-500">*</span>
              </label>
              <HierarchicalSelect
                options={hierarchicalDeviceTypeOptions}
                value={formData.type}
                onChange={createOnChangeHandler('type')}
                placeholder="请选择设备类型"
                required={true}
                disabled={disabled || disableTypeSelection}
              />
              {errors.type && <p className="mt-1 text-sm text-red-600">{errors.type}</p>}
            </div>

            {/* 保密编号 */}
            <div className="flex flex-col">
              <ValidatedInput
                label="保密编号"
                value={formData.securityCode}
                onChange={createOnChangeHandler('securityCode')}
                error={errors.securityCode}
                placeholder="请输入保密编号"
                required
                disabled={disabled}
                maxByteLength={108}
                showCounter={true}
              />
            </div>

            {/* 密级 */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-gray-700 mb-2">
                密级 <span className="text-red-500">*</span>
              </label>
              <Select
                name="securityLevel"
                value={formData.securityLevel}
                onChange={handleInputChange}
                options={securityLevelDictionaryItems}
                placeholder="请选择密级"
                required
                error={errors.securityLevel}
                disabled={disabled}
              />
            </div>

            {/* 所属部门 */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-gray-700 mb-2">
                所属部门 <span className="text-red-500">*</span>
              </label>
              <DepartmentTreeSelect
                options={departmentTreeOptions}
                value={formData.department}
                onChange={createOnChangeHandler('department')}
                placeholder="请选择所属部门"
                required={true}
                disabled={disabled || disableDepartmentSelection}
              />
              {errors.department && <p className="mt-1 text-sm text-red-600">{errors.department}</p>}
            </div>

            {/* 责任人 */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-gray-700 mb-2">
                责任人 <span className="text-red-500">*</span>
              </label>
              <ResponsiblePersonSelect
                name="responsible"
                value={formData.responsible}
                onChange={handleInputChange}
                options={personOptions}
                placeholder="请选择责任人"
                required={true}
                error={errors.responsible}
                disabled={disabled || disableResponsibleSelection || (!formData.department || personOptions.length === 0)}
              />
            </div>

            {/* 名称 */}
            <div className="flex flex-col">
              <ValidatedCombobox
                label="名称"
                value={formData.name}
                onChange={createOnChangeHandler('name')}
                options={fieldOptions.name?.map(val => ({ code: val, value: val })) || []}
                error={errors.name}
                placeholder="请输入名称"
                required
                disabled={disabled}
                maxByteLength={108}
                showCounter={true}
              />
            </div>

            {/* 型号 */}
            <div className="flex flex-col">
              <ValidatedCombobox
                label="型号"
                value={formData.model}
                onChange={createOnChangeHandler('model')}
                options={fieldOptions.model?.map(val => ({ code: val, value: val })) || []}
                error={errors.model}
                placeholder="请选择或输入型号"
                required
                disabled={disabled}
                maxByteLength={108}
                showCounter={true}
              />
            </div>

            {/* 放置地点 */}
            <div className="flex flex-col">
              <ValidatedCombobox
                label="放置地点"
                value={formData.location}
                onChange={createOnChangeHandler('location')}
                options={fieldOptions.location?.map(val => ({ code: val, value: val })) || []}
                error={errors.location}
                placeholder="请选择或输入放置地点"
                required
                disabled={disabled}
                maxByteLength={260}
                showCounter={true}
              />
            </div>

            {/* 启用时间 */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-gray-700 mb-2">
                启用时间 <span className="text-red-500">*</span>
              </label>
              <DatePicker
                value={formData.startTime}
                onChange={(date) => {
                  // 创建合成事件对象以兼容原有的handleInputChange
                  const syntheticEvent = {
                    target: { name: 'startTime', value: date }
                  } as React.ChangeEvent<HTMLInputElement>;
                  handleInputChange(syntheticEvent);
                }}
                placeholder="请选择启用时间"
                error={!!errors.startTime}
                disabled={disabled}
              />
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>
              )}
            </div>

            {/* 使用情况 */}
            <div className="flex flex-col">
              <label className="block text-sm font-bold text-gray-700 mb-2">
                使用情况 <span className="text-red-500">*</span>
              </label>
              <Select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                options={statusDictionaryItems}
                placeholder="请选择使用情况"
                required
                error={errors.status}
                disabled={disabled}
              />
            </div>

            {/* 用途 */}
            <div className="flex flex-col">
              <ValidatedCombobox
                label="用途"
                value={formData.purpose}
                onChange={createOnChangeHandler('purpose')}
                options={fieldOptions.purpose?.map(val => ({ code: val, value: val })) || []}
                error={errors.purpose}
                placeholder="请选择或输入用途"
                required
                disabled={disabled}
                maxByteLength={512}
                showCounter={true}
              />
            </div>

            {/* 扩展字段 - 直接整合到网格中 */}
            {(extFields.length > 0 || formExtFields.length > 0) && (
              <>
                {/* 优先使用表单扩展字段，如果没有则使用props传入的扩展字段 */}
                {(formExtFields.length > 0 ? formExtFields : extFields).map((field: ExtFieldDefinition) => {
                  // 检查是否联网字段的逻辑
                  const isNetworkField = field.title.toLowerCase().includes('ip') ||
                                        field.key.toLowerCase().includes('ip') ||
                                        field.title.toLowerCase().includes('mac') ||
                                        field.key.toLowerCase().includes('mac');

                  // 查找是否联网字段的值
                  const networkingField = (formExtFields.length > 0 ? formExtFields : extFields).find(f =>
                    f.title === '是否联网' || f.key === 'is_networking' || f.title.includes('是否联网')
                  );

                  // 如果是网络相关字段且是否联网为"否"，则隐藏
                  if (isNetworkField && networkingField) {
                    const networkingValue = extFieldValues[networkingField.key] || '';
                    if (networkingValue === '否' || networkingValue === 'no' || networkingValue === '0') {
                      return null; // 隐藏字段
                    }
                  }

                  return (
                    <div key={field.key} className="flex flex-col">
                      <ExtField
                        field={field}
                        value={extFieldValues[field.key] || ''}
                        onChange={onExtFieldChange}
                        disabled={disabled}
                        errors={errors}
                        setErrors={setErrors}
                      />
                    </div>
                  );
                })}
              </>
            )}
          </div>
        </div>

      {/* 按钮区域 - 根据hideButtons属性决定是否显示 */}
      {!hideButtons && (
        <div className="flex-shrink-0 flex justify-end space-x-3 sm:space-x-4 pt-4 border-t bg-white">
          <button
            type="button"
            onClick={onCancel}
            className="px-3 py-1.5 sm:px-4 sm:py-2 border border-gray-300 rounded-md text-sm sm:text-base text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            disabled={disabled}
          >
            取消
          </button>
          <button
            type="submit"
            className="px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-600 rounded-md text-sm sm:text-base text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            disabled={disabled}
          >
            保存
          </button>
        </div>
      )}
    </form>
    </div>
  );
};

export default InventoryForm;