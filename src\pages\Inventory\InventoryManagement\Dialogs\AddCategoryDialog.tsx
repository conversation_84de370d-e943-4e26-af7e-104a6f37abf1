import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';
import { Folder } from 'lucide-react';
import { DeviceCategory } from '../../../../types/inventory';
import CustomDropdownSelect from '../../../../components/ui/CustomDropdownSelect';
import { useDialogEscapeKey } from '../../../../hooks/base/useEscapeKey';
import { useValidatedField } from '../../../../hooks/base/useValidatedField';
import { ValidatedInput } from '../../../../components/ui/FormComponents';
import IconSelector from '../../../../components/IconSelector';
import { IconType, ICON_DESCRIPTIONS } from '../../../../hooks/Inventory/useCategoryIcons';
import { getIconComponent } from '../../../../utils/iconMapping';
import GlobalConfigService from '../../../../services/globalConfigService';

// 添加分类对话框属性
interface AddCategoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (parentId: string, categoryName: string, categoryType: 'type' | 'device', customIcon?: string) => Promise<void>;
  deviceCategories: DeviceCategory[];
  mode: 'type' | 'device'; // 'type'表示添加一级分类，'device'表示添加二级分类
  selectedCategory?: string; // 当前选中的分类ID
  disableParentSelection?: boolean; // 是否禁用父级分类选择，用于从树状图联动添加时
}

const AddCategoryDialog: React.FC<AddCategoryDialogProps> = ({
  isOpen,
  onClose,
  onAdd,
  deviceCategories,
  mode,
  selectedCategory,
  disableParentSelection = false
}) => {
  // 状态
  const [name, setName] = useState('');
  const [parentId, setParentId] = useState('all'); // 默认为根节点
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedIcon, setSelectedIcon] = useState<IconType | undefined>();
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [companyName, setCompanyName] = useState('公司名称');

  // 使用单字段验证Hook
  const validationConfig = {
    name: mode === 'type' ? 'parentCategoryName' : 'subCategoryName'
  };

  const nameField = useValidatedField('name', name, setName, validationConfig);

  // 解构出稳定的函数引用，避免useEffect依赖问题
  const { clearErrors: clearNameErrors } = nameField;

  // 使用统一的ESC键处理
  useDialogEscapeKey(isOpen, onClose, {
    debugId: 'AddCategoryDialog',
    priority: 100
  });

  // 获取公司名称
  useEffect(() => {
    const globalConfigService = GlobalConfigService.getInstance();
    const cachedName = globalConfigService.getCachedCompanyName();
    if (cachedName && cachedName !== '公司名称') {
      setCompanyName(cachedName);
    }
  }, []);

  // 监听对话框关闭，重置所有状态到初始值
  useEffect(() => {
    if (!isOpen) {
      // 对话框关闭时，重置所有状态到初始值
      setName('');
      setParentId('all');
      setError(null);
      setSelectedIcon(undefined);
      setShowIconSelector(false);
      // 清除验证错误状态
      clearNameErrors();
      return;
    }
  }, [isOpen, clearNameErrors]);

  // 根据选中的分类和模式设置初始父级ID
  useEffect(() => {
    if (!isOpen) return;

    if (mode === 'type') {
      // 添加类型时固定选择根节点
      setParentId('all');
    } else if (selectedCategory) {
      // 根据当前选中节点设置父级ID

      // 查找选中的分类节点
      const findCategoryById = (categories: DeviceCategory[]): DeviceCategory | null => {
        for (const category of categories) {
          if (category.id === selectedCategory) {
            return category;
          }
          if (category.children) {
            const found = findCategoryById(category.children);
            if (found) return found;
          }
        }
        return null;
      };

      // 查找节点的父节点ID
      const findParentId = (categoryId: string): string => {
        // 如果是根节点，返回根节点ID
        if (categoryId === 'all') return 'all';

        // 查找所有分类中的父节点
        const findParent = (categories: DeviceCategory[], targetId: string): string | null => {
          for (const category of categories) {
            if (category.children) {
              // 检查子节点中是否有目标ID
              if (category.children.some(child => child.id === targetId)) {
                return category.id;
              }
              // 递归查找
              const found = findParent(category.children, targetId);
              if (found) return found;
            }
          }
          return null;
        };

        const parentId = findParent(deviceCategories, categoryId);
        return parentId || 'all'; // 如果找不到父节点，默认为根节点
      };

      const category = findCategoryById(deviceCategories);

      if (category) {
        if (mode === 'device') {
          // 如果是添加设备(二级分类)
          if (category.id === 'all') {
            // 如果选中的是根节点，默认选择第一个一级分类(如果存在)
            if (deviceCategories.length > 0 && deviceCategories[0]?.children && deviceCategories[0].children.length > 0) {
              setParentId(deviceCategories[0].children[0]?.id || 'all');
            }
          } else if (category.id.startsWith('parent-')) {
            // 如果选中的是一级分类，直接使用该分类作为父级
            // 不再依赖children属性，而是检查ID前缀
            setParentId(category.id);
          } else {
            // 如果选中的是二级分类，使用其父级分类
            const parentId = findParentId(category.id);
            setParentId(parentId);
          }
        }
      }
    } else {
      // 如果没有选中的分类，但是模式是device
      if (mode === 'device') {
        // 默认选择第一个一级分类
        if (deviceCategories.length > 0 && deviceCategories[0]?.children && deviceCategories[0].children.length > 0) {
          setParentId(deviceCategories[0].children[0]?.id || 'all');
        }
      }
    }

    // 注意：名称和错误状态的重置已在关闭时的useEffect中处理，这里不需要重复重置
  }, [mode, deviceCategories, isOpen, selectedCategory]);

  // 如果对话框未打开，则不渲染内容
  if (!isOpen) return null;

  // 获取可选择的父级节点列表
  const getParentCategories = (): DeviceCategory[] => {
    if (mode === 'type') {
      // 添加类型时只能选择根节点
      return deviceCategories.filter(cat => cat.id === 'all');
    } else {
      // 添加设备时可以选择所有一级分类
      if (deviceCategories.length > 0 && deviceCategories[0]?.children) {
        return deviceCategories[0].children;
      }
      return [];
    }
  };

  // 父级节点列表
  const parentCategories = getParentCategories();

  // 获取选中图标的组件
  const getSelectedIconComponent = () => {
    return getIconComponent(selectedIcon, "w-5 h-5");
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 表单验证
    if (nameField.error || !name.trim()) {
      setError('请检查输入内容');
      return;
    }

    // 检查父级节点是否有效
    if (!parentId) {
      setError(mode === 'type' ? '请选择公司名称' : '请选择父级分类');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // 调用添加方法，传递自定义图标
      await onAdd(parentId, name, mode, selectedIcon);

      // 关闭对话框
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : '添加失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-auto">
        <div className="p-6">
          {/* 对话框标题 */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-3">
              {showIconSelector && (
                <button
                  onClick={() => setShowIconSelector(false)}
                  className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                  title="返回"
                >
                  <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <h2 className="text-xl font-semibold text-gray-800">
                {showIconSelector
                  ? '选择图标'
                  : (mode === 'type' ? '添加分类' : '添加类型')
                }
              </h2>
            </div>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
              title="关闭"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert
              message="错误"
              description={error}
              type="error"
              showIcon
              className="mb-4"
              closable
              onClose={() => setError(null)}
            />
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="mb-4 flex items-center space-x-2">
              <Spin />
              <span className="text-blue-600">正在处理请求...</span>
            </div>
          )}

          {/* 内容区域 */}
          {showIconSelector ? (
            // 图标选择器 - 无重复标题
            <div className="space-y-4">
              <IconSelector
                selectedIcon={selectedIcon}
                onSelectIcon={(icon) => {
                  setSelectedIcon(icon);
                  setShowIconSelector(false);
                }}
                onClose={() => setShowIconSelector(false)}
              />
            </div>
          ) : (
            // 表单
            <form onSubmit={handleSubmit} className="space-y-4">
            {/* 父级节点提示 */}
            {mode === 'type' ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">
                    将在{companyName}下创建新分类
                  </span>
                </div>
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  父级分类 <span className="text-red-500">*</span>
                </label>
                <CustomDropdownSelect
                  options={parentCategories.map(category => ({ value: category.id, label: category.name }))}
                  value={parentId}
                  onChange={setParentId}
                  placeholder="请选择父级分类"
                  disabled={isLoading || disableParentSelection}
                  className="w-full"
                />
              </div>
            )}

            {/* 名称输入 */}
            <ValidatedInput
              label={mode === 'type' ? '分类名称' : '类型名称'}
              {...nameField}
              placeholder={mode === 'type' ? '请输入分类名称' : '请输入类型名称'}
              disabled={isLoading}
              preset="category"
              autoFocus={true}
            />

            {/* 图标选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                分类图标 <span className="text-gray-500">(可选)</span>
              </label>
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={() => setShowIconSelector(true)}
                  className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  disabled={isLoading}
                >
                  {selectedIcon ? (
                    <>
                      {getSelectedIconComponent()}
                      <span className="text-sm text-gray-700">
                        {ICON_DESCRIPTIONS[selectedIcon]}
                      </span>
                    </>
                  ) : (
                    <>
                      <Folder className="w-5 h-5 text-gray-400" />
                      <span className="text-sm text-gray-500">选择图标</span>
                    </>
                  )}
                </button>

                {selectedIcon && (
                  <button
                    type="button"
                    onClick={() => setSelectedIcon(undefined)}
                    className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                    disabled={isLoading}
                  >
                    清除
                  </button>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                选择一个图标来代表此分类，如果不选择将使用默认图标
              </p>
            </div>

            {/* 按钮 */}
            <div className="flex justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 rounded-md text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isLoading}
              >
                保存
              </button>
            </div>
          </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddCategoryDialog;