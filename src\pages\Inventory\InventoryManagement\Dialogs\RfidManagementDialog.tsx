import React, { useState, useEffect } from 'react';
import { X, Tag, Plus, Trash2, CheckCircle, AlertCircle, Users } from 'lucide-react';
import DialogBase from '../../../../components/ui/DialogBase';
import { InventoryItem } from '../../../../types/inventory';
import useRfidManagement from '../../../../hooks/Inventory/useRfidManagement';
import { RfidBindResult, RfidDeleteResult } from '../../../../services/Inventory/rfidManagementService';
import RfidResultDialog from './RfidResultDialog';

/**
 * RFID管理对话框属性接口
 */
interface RfidManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItems: InventoryItem[];
  onSelectItems: (items: InventoryItem[]) => void;
  onShowSelectionTip?: () => void; // 新增：显示选择提示的回调
}

/**
 * RFID管理对话框组件
 * 只负责渲染，所有逻辑都在Hook中处理
 */
const RfidManagementDialog: React.FC<RfidManagementDialogProps> = ({
  isOpen,
  onClose,
  selectedItems,
  onSelectItems,
  onShowSelectionTip
}) => {
  // 删除确认对话框状态
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  // 使用RFID管理Hook，传入初始选中项目
  const {
    // 数据状态
    isLoading,
    lastBindResult,
    lastDeleteResult,
    error,

    // UI状态
    operationType,
    showResult,
    showResultDialog,
    localSelectedItems,

    // 数据操作方法
    clearError,
    resetStates,
    closeResultDialog,

    // UI状态管理方法
    handleLocalItemToggle,

    // 业务操作方法
    executeBindRfid,
    executeDeleteRfid
  } = useRfidManagement(selectedItems);

  // 初始化状态（当对话框打开时重置状态，但不要在显示结果时重置）
  useEffect(() => {
    if (isOpen && !showResultDialog) {
      resetStates();
    }
  }, [isOpen, showResultDialog, resetStates]);

  // 处理绑定RFID（纯UI事件处理）
  const handleBindRfid = async () => {
    try {
      await executeBindRfid();
      // 成功提示会通过Hook的事件处理器自动显示
    } catch (error) {
      // 错误已经通过Hook的error状态处理，这里不需要额外的alert
      console.error('RFID绑定操作失败:', error);
    }
  };

  // 处理删除RFID（显示确认对话框）
  const handleDeleteRfid = () => {
    setShowDeleteConfirm(true);
  };

  // 确认删除RFID
  const confirmDeleteRfid = async () => {
    setShowDeleteConfirm(false);
    try {
      await executeDeleteRfid();
      // 成功提示会通过Hook的事件处理器自动显示
    } catch (error) {
      // 错误已经通过Hook的error状态处理，这里不需要额外的alert
      console.error('RFID删除操作失败:', error);
    }
  };

  // 取消删除RFID
  const cancelDeleteRfid = () => {
    setShowDeleteConfirm(false);
  };

  // 处理添加设备（回到总表进行勾选）
  const handleAddDevices = () => {
    // 关闭RFID管理对话框
    handleClose();
    // 显示选择提示
    if (onShowSelectionTip) {
      onShowSelectionTip();
    }
  };

  // 处理删除设备（从RFID管理列表中移除，并同步总表选中状态）
  const handleRemoveDevice = (deviceId: string) => {
    const deviceToRemove = localSelectedItems.find(item => item.id === deviceId);
    if (deviceToRemove) {
      // 先计算移除后的列表（在修改本地状态之前）
      const updatedItems = localSelectedItems.filter(item => item.id !== deviceId);

      // 从本地选中列表中移除
      handleLocalItemToggle(deviceToRemove);

      // 同步更新总表的选中状态
      onSelectItems(updatedItems);
    }
  };

  // 处理关闭对话框（纯UI事件处理）
  const handleClose = () => {
    resetStates();
    onClose();
  };



  // 渲染设备列表 - 响应式优化
  const renderDeviceList = () => {
    if (localSelectedItems.length === 0) {
      return (
        <div className="text-center py-8 sm:py-12 text-gray-500">
          <Users className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 text-gray-300" />
          <p className="text-base sm:text-lg font-medium mb-2">暂无选中的设备</p>
          <p className="text-xs sm:text-sm">请点击上方的"+"按钮回到总表选择设备</p>
        </div>
      );
    }

    // 计算表格高度：减少行高后调整，根据设备数量动态调整，最小300px，最大600px
    const tableHeight = Math.min(Math.max(localSelectedItems.length * 45 + 50, 300), 600);

    return (
      <div
        className="overflow-y-auto border-gray-100"
        style={{ maxHeight: `${tableHeight}px` }}
      >
        <table className="w-full text-xs sm:text-sm">
          <thead className="bg-gray-50 sticky top-0 z-10">
            <tr>
              <th className="px-2 sm:px-4 py-1.5 sm:py-2 text-left font-medium text-gray-700 w-20 sm:w-32">设备ID</th>
              <th className="px-2 sm:px-4 py-1.5 sm:py-2 text-left font-medium text-gray-700">设备名称</th>
              <th className="px-2 sm:px-4 py-1.5 sm:py-2 text-left font-medium text-gray-700 w-32 sm:w-48">RFID安全码</th>
              <th className="px-2 sm:px-4 py-1.5 sm:py-2 text-center font-medium text-gray-700 w-16 sm:w-24">操作</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {localSelectedItems.map((item, index) => (
              <tr
                key={item.id}
                className={`hover:bg-gray-50 transition-colors ${
                  index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                }`}
              >
                <td className="px-2 sm:px-6 py-2 sm:py-2.5 font-medium text-gray-900 font-mono text-xs">
                  {item.id}
                </td>
                <td className="px-2 sm:px-6 py-2 sm:py-2.5 text-gray-700">
                  <div className="max-w-xs truncate" title={item.name}>
                    {item.name}
                  </div>
                </td>
                <td className="px-2 sm:px-6 py-2 sm:py-2.5 text-gray-700">
                  {item.securityRfid ? (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {item.securityRfid}
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                      未绑定
                    </span>
                  )}
                </td>
                <td className="px-2 sm:px-6 py-2 sm:py-2.5 text-center">
                  <button
                    onClick={() => handleRemoveDevice(item.id)}
                    className="text-rose-600 hover:text-rose-800 hover:bg-rose-50 p-1.5 rounded-full transition-colors"
                    title="从列表中移除"
                  >
                    <X className="h-3 w-3 sm:h-4 sm:w-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* 设备数量统计和提示 - 更新颜色样式保持一致 */}
        {localSelectedItems.length > 0 && (
          <div className="bg-gray-50 px-4 sm:px-6 py-2 sm:py-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs sm:text-sm text-gray-600">
              <span>
                共 <span className="font-medium text-gray-900">{localSelectedItems.length}</span> 个设备
              </span>
              {localSelectedItems.length > 20 && (
                <span className="text-amber-600">
                  ⚠️ 设备较多，建议分批处理RFID操作
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };



  return (
    <DialogBase
      isOpen={isOpen}
      onClose={handleClose}
      width="100%"
      maxWidth="64rem"
      maxHeight="95vh"
      animation="fade"
      closeOnOverlayClick={false}
    >
      <div className="flex flex-col h-full max-h-[95vh]">
        {/* 对话框标题 - 固定高度，将添加按钮移到标题旁边 */}
        <div className="flex-shrink-0 flex items-center justify-between px-4 sm:px-6 py-2.5 border-b">
          <div className="flex items-center">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-800">RFID码管理</h2>
            <button
              onClick={handleAddDevices}
              className="ml-2 sm:ml-3 w-5 h-5 sm:w-6 sm:h-6 text-green-500 hover:text-green-600 hover:bg-green-50 rounded-full flex items-center justify-center transition-colors"
              title="添加设备"
            >
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 stroke-2" />
            </button>
            <p className="text-xs sm:text-sm text-blue-600 ml-3">
              已选择 {localSelectedItems.length} 个设备
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 flex-shrink-0"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 对话框内容 - 可滚动区域，与导出界面一致的内边距 */}
        <div className="flex-1 overflow-y-auto min-h-0 p-3 sm:p-4">{/* 响应式内边距 */}
          {/* 错误提示 - 与导出界面一致的样式和间距 */}
          {error && (
            <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm flex items-start">
              <AlertCircle className="h-4 w-4 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <p className="text-red-700 mb-2">{error}</p>
                <button
                  onClick={clearError}
                  className="text-xs text-red-600 hover:text-red-800 font-medium"
                >
                  关闭
                </button>
              </div>
            </div>
          )}



          {/* 设备列表区域 - 扩大表格，减少空白 */}
          <div className="mb-2">
            <div className="border border-gray-200 rounded-lg shadow-sm">
              {renderDeviceList()}
            </div>
          </div>

        {/* 简单成功提示 */}
        {showResult && !isLoading && !error && (
          <div className="mt-4 p-4 bg-emerald-50 rounded-lg border border-emerald-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-emerald-600 mr-2" />
                <span className="text-emerald-700 font-medium">
                  {operationType === 'bind' ? 'RFID码添加成功！' : 'RFID码删除成功！'}
                </span>
              </div>
              <button
                onClick={resetStates}
                className="text-emerald-600 hover:text-emerald-800 hover:bg-emerald-100 p-1 rounded transition-colors"
                title="关闭提示"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <div className="text-emerald-600 text-sm mt-1">
              设备列表已自动更新，点击查看详细结果
            </div>
          </div>
        )}

          {/* 加载状态 - 与导出界面一致的样式 */}
          {isLoading && (
            <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent mr-2"></div>
              <span className="text-blue-700">
                {operationType === 'bind' ? '正在绑定RFID码...' :
                 operationType === 'delete' ? '正在删除RFID码...' : '正在处理...'}
              </span>
            </div>
          )}
        </div>

        {/* 对话框底部按钮 - 固定在底部，与导出菜单保持一致 */}
        <div className="flex-shrink-0 px-3 sm:px-4 py-2.5 border-t bg-white flex justify-end space-x-2">
          <button
            onClick={handleBindRfid}
            disabled={isLoading || localSelectedItems.length === 0}
            className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-colors"
          >
            <Tag className="h-3 w-3 mr-1" />
            添加RFID码
          </button>
          <button
            onClick={handleDeleteRfid}
            disabled={isLoading || localSelectedItems.length === 0}
            className="px-3 py-1.5 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-colors"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            删除RFID码
          </button>
        </div>
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0 w-10 h-10 bg-rose-100 rounded-full flex items-center justify-center">
                <AlertCircle className="w-6 h-6 text-rose-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-slate-900">确认删除RFID码</h3>
              </div>
            </div>
            <div className="mb-6">
              <p className="text-sm text-slate-600">
                您确定要删除选中的 <span className="font-medium text-red-600">{localSelectedItems.length}</span> 个设备的RFID码吗？
              </p>
              {/* <p className="text-xs text-rose-600 mt-2">
                此操作不可撤销，删除后需要重新绑定RFID码。
              </p> */}
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDeleteRfid}
                className="px-4 py-2 text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
              >
                取消
              </button>
              <button
                onClick={confirmDeleteRfid}
                className="px-4 py-2 bg-rose-500 text-white rounded-lg hover:bg-rose-600 transition-colors"
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* RFID操作结果对话框 */}
      <RfidResultDialog
        isOpen={showResultDialog}
        onClose={closeResultDialog}
        operationType={operationType || (lastDeleteResult ? 'delete' : 'bind')}
        bindResult={lastBindResult}
        deleteResult={lastDeleteResult}
      />
    </DialogBase>
  );
};

export default RfidManagementDialog;
